"""
AI processor with unified interface for multiple LLM providers.
"""

import json
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from decimal import Decimal

from config.settings import get_settings
from config.llm_config import create_llm_config, create_provider, LLMProvider
from models.transaction import Transaction, TransactionBatch
from models.bank_config import BankConfig
from utils.logger import get_logger, log_execution_time
from utils.date_parser import parse_date


class AIProcessor:
    """Main AI processor with unified interface for all providers."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.providers: Dict[str, LLMProvider] = {}
        self.usage_stats = {
            'total_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'provider_usage': {}
        }
        
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all configured AI providers."""
        provider_configs = {
            'openai': {
                'api_key': self.settings.openai_api_key,
                'model': self.settings.openai_model
            },
            'anthropic': {
                'api_key': self.settings.anthropic_api_key,
                'model': self.settings.anthropic_model
            },
            'google': {
                'api_key': self.settings.google_api_key,
                'model': self.settings.google_model
            },
            'mistral': {
                'api_key': self.settings.mistral_api_key,
                'model': self.settings.mistral_model,
                'base_url': 'https://api.mistral.ai/v1'
            },
            'deepseek': {
                'api_key': self.settings.deepseek_api_key,
                'model': self.settings.deepseek_model,
                'base_url': 'https://api.deepseek.com/v1'
            },
            'openrouter': {
                'api_key': self.settings.openrouter_api_key,
                'model': self.settings.openrouter_model,
                'base_url': 'https://openrouter.ai/api/v1'
            },
            'custom': {
                'api_key': self.settings.custom_llm_api_key,
                'model': 'custom-model',
                'base_url': self.settings.custom_llm_endpoint
            }
        }
        
        for provider_name, config in provider_configs.items():
            if config['api_key']:
                try:
                    llm_config = create_llm_config(
                        provider=provider_name,
                        api_key=config['api_key'],
                        model=config['model'],
                        base_url=config.get('base_url'),
                        temperature=self.settings.ai_temperature,
                        max_tokens=self.settings.ai_max_tokens,
                        timeout=self.settings.ai_timeout
                    )
                    
                    provider = create_provider(provider_name, llm_config)
                    if provider.validate_config():
                        self.providers[provider_name] = provider
                        self.logger.info(f"Initialized {provider_name} provider")
                    else:
                        self.logger.warning(f"Invalid configuration for {provider_name} provider")
                
                except Exception as e:
                    self.logger.error(f"Failed to initialize {provider_name} provider: {e}")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return list(self.providers.keys())
    
    async def identify_bank(self, statement_text: str, provider: str = None) -> Dict[str, Any]:
        """Identify bank from statement text using AI."""
        if not provider:
            provider = self.settings.default_ai_provider
        
        if provider not in self.providers:
            raise ValueError(f"Provider {provider} not available")
        
        prompt = self._get_bank_identification_prompt(statement_text)
        
        with log_execution_time(self.logger, f"bank_identification_{provider}"):
            try:
                response = await self._make_ai_request(provider, prompt)
                result = self._parse_bank_identification_response(response)
                
                self.logger.info(f"Bank identified: {result.get('bank_name', 'Unknown')}")
                return result
            
            except Exception as e:
                self.logger.error(f"Bank identification failed: {e}")
                return {
                    'bank_name': 'Unknown',
                    'country': 'Unknown',
                    'format_type': 'Unknown',
                    'language': 'Unknown',
                    'confidence': 0.0
                }
    
    async def extract_transactions(
        self, 
        statement_text: str, 
        bank_config: Optional[BankConfig] = None,
        provider: str = None
    ) -> TransactionBatch:
        """Extract transactions from statement text using AI."""
        if not provider:
            provider = self.settings.default_ai_provider
        
        if provider not in self.providers:
            raise ValueError(f"Provider {provider} not available")
        
        prompt = self._get_transaction_extraction_prompt(statement_text, bank_config)
        
        with log_execution_time(self.logger, f"transaction_extraction_{provider}"):
            try:
                response = await self._make_ai_request(provider, prompt)
                transactions = self._parse_transaction_response(response)
                
                # Create transaction batch
                batch = TransactionBatch(
                    transactions=transactions,
                    processed_at=datetime.now(),
                    ai_provider=provider,
                    confidence_score=self._calculate_batch_confidence(transactions)
                )
                
                self.logger.info(f"Extracted {len(transactions)} transactions using {provider}")
                return batch
            
            except Exception as e:
                self.logger.error(f"Transaction extraction failed: {e}")
                return TransactionBatch(
                    transactions=[],
                    processed_at=datetime.now(),
                    ai_provider=provider,
                    confidence_score=0.0
                )
    
    async def _make_ai_request(self, provider: str, prompt: str, **kwargs) -> str:
        """Make AI request with retry logic and error handling."""
        ai_provider = self.providers[provider]
        
        for attempt in range(self.settings.retry_attempts):
            try:
                start_time = time.time()
                response = await ai_provider.generate_response(prompt, **kwargs)
                end_time = time.time()
                
                # Update usage stats
                self._update_usage_stats(provider, prompt, response, end_time - start_time)
                
                return response
            
            except Exception as e:
                self.logger.warning(f"AI request attempt {attempt + 1} failed: {e}")
                if attempt < self.settings.retry_attempts - 1:
                    await asyncio.sleep(self.settings.retry_delay * (attempt + 1))
                else:
                    raise
    
    def _update_usage_stats(self, provider: str, prompt: str, response: str, duration: float):
        """Update usage statistics."""
        ai_provider = self.providers[provider]
        
        # Estimate tokens and cost
        estimated_cost = ai_provider.estimate_cost(prompt, response)
        estimated_tokens = (len(prompt) + len(response)) // 4  # Rough estimation
        
        # Update global stats
        self.usage_stats['total_requests'] += 1
        self.usage_stats['total_tokens'] += estimated_tokens
        self.usage_stats['total_cost'] += estimated_cost
        
        # Update provider-specific stats
        if provider not in self.usage_stats['provider_usage']:
            self.usage_stats['provider_usage'][provider] = {
                'requests': 0,
                'tokens': 0,
                'cost': 0.0,
                'avg_response_time': 0.0
            }
        
        provider_stats = self.usage_stats['provider_usage'][provider]
        provider_stats['requests'] += 1
        provider_stats['tokens'] += estimated_tokens
        provider_stats['cost'] += estimated_cost
        
        # Update average response time
        current_avg = provider_stats['avg_response_time']
        new_avg = (current_avg * (provider_stats['requests'] - 1) + duration) / provider_stats['requests']
        provider_stats['avg_response_time'] = new_avg
        
        # Log API usage
        self.logger.debug(f"API usage - {provider}: {estimated_tokens} tokens, ${estimated_cost:.4f}, {duration:.2f}s")
    
    def _get_bank_identification_prompt(self, statement_text: str) -> str:
        """Generate bank identification prompt."""
        return f"""Identify the bank from this statement text. Look carefully for bank logos, headers, and official bank names.

Important bank name mappings:
- If you see "ADCB" or "Abu Dhabi Commercial Bank" → use "ADCB"
- If you see "Emirates NBD" → use "Emirates NBD"
- If you see "FAB" or "First Abu Dhabi Bank" → use "FAB"
- Use the exact official bank name as it appears in the document

Text: {statement_text[:2000]}

Response format (JSON only):
{{
  "bank_name": "Exact Bank Name",
  "country": "Country",
  "format_type": "PDF/CSV/etc",
  "language": "Language",
  "confidence": 0.95
}}"""
    
    def _get_transaction_extraction_prompt(self, statement_text: str, bank_config: Optional[BankConfig] = None) -> str:
        """Generate transaction extraction prompt."""
        base_prompt = """You are a bank statement processor. Extract transaction details from the following bank statement text.

CRITICAL RULES FOR DEBIT/CREDIT CLASSIFICATION:
1. DEBIT (money going OUT of account): withdrawals, payments, fees, transfers out, purchases
2. CREDIT (money coming INTO account): deposits, salary, transfers in, refunds, interest earned

Requirements:
1. Extract only transaction data (ignore headers, footers, summaries, opening/closing balances)
2. For each transaction, identify: Date, Description, Debit Amount, Credit Amount, Balance
3. Normalize dates to DD/MM/YYYY format
4. Extract amounts as numbers without currency symbols
5. Clean and standardize transaction descriptions
6. Ignore non-transaction lines (balances, fees explanations, etc.)
7. CAREFULLY determine if each transaction is a debit (outgoing) or credit (incoming)
8. Extract running balance if available in the statement

Output format (JSON array only):
[
  {
    "date": "DD/MM/YYYY",
    "description": "Transaction description",
    "debit": 100.50,
    "credit": null,
    "balance": 1500.25
  },
  {
    "date": "DD/MM/YYYY",
    "description": "Another transaction",
    "debit": null,
    "credit": 250.00,
    "balance": 1750.25
  }
]

Bank Statement Text:
"""
        
        # Add bank-specific instructions if available
        if bank_config:
            base_prompt += f"\nBank: {bank_config.bank_name}\n"
            base_prompt += f"Date format: {bank_config.date_format}\n"
            base_prompt += f"Currency: {bank_config.currency}\n"
        
        return base_prompt + statement_text
    
    def _parse_bank_identification_response(self, response: str) -> Dict[str, Any]:
        """Parse bank identification response."""
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                raise ValueError("No valid JSON found in response")
        
        except Exception as e:
            self.logger.error(f"Failed to parse bank identification response: {e}")
            return {
                'bank_name': 'Unknown',
                'country': 'Unknown',
                'format_type': 'Unknown',
                'language': 'Unknown',
                'confidence': 0.0
            }
    
    def _parse_transaction_response(self, response: str) -> List[Transaction]:
        """Parse transaction extraction response."""
        transactions = []
        
        try:
            # Extract JSON array from response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                transaction_data = json.loads(json_str)
                
                for item in transaction_data:
                    try:
                        # Parse date
                        date_obj = parse_date(item.get('date', ''))
                        if not date_obj:
                            continue
                        
                        # Parse amounts
                        debit = None
                        credit = None
                        
                        if item.get('debit') is not None:
                            try:
                                debit = Decimal(str(item['debit']))
                            except:
                                pass
                        
                        if item.get('credit') is not None:
                            try:
                                credit = Decimal(str(item['credit']))
                            except:
                                pass
                        
                        # Create transaction
                        transaction = Transaction(
                            date=date_obj,
                            description=item.get('description', '').strip(),
                            debit=debit,
                            credit=credit,
                            original_text=str(item),
                            confidence_score=0.8  # Default confidence
                        )
                        
                        transactions.append(transaction)
                    
                    except Exception as e:
                        self.logger.warning(f"Failed to parse transaction: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"Failed to parse transaction response: {e}")
        
        return transactions
    
    def _calculate_batch_confidence(self, transactions: List[Transaction]) -> float:
        """Calculate overall confidence score for a batch of transactions."""
        if not transactions:
            return 0.0
        
        total_confidence = sum(t.confidence_score or 0.0 for t in transactions)
        return total_confidence / len(transactions)
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        return self.usage_stats.copy()
    
    def reset_usage_stats(self):
        """Reset usage statistics."""
        self.usage_stats = {
            'total_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'provider_usage': {}
        }


class AIProcessorManager:
    """Manager for multiple AI processors with fallback support."""

    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.processor = AIProcessor()
        self.fallback_providers = []

        # Set up fallback chain
        self._setup_fallback_chain()

    def _setup_fallback_chain(self):
        """Setup fallback provider chain."""
        available_providers = self.processor.get_available_providers()

        # Remove default provider from available list
        if self.settings.default_ai_provider in available_providers:
            available_providers.remove(self.settings.default_ai_provider)

        # Create custom fallback order based on user's default provider
        if self.settings.default_ai_provider == 'deepseek':
            # If DeepSeek is primary, use OpenRouter as first fallback
            preferred_order = ['openrouter', 'openai', 'anthropic', 'google', 'mistral', 'custom']
        elif self.settings.default_ai_provider == 'openrouter':
            # If OpenRouter is primary, use DeepSeek as first fallback
            preferred_order = ['deepseek', 'openai', 'anthropic', 'google', 'mistral', 'custom']
        else:
            # Default fallback order for other providers
            preferred_order = ['deepseek', 'openrouter', 'openai', 'anthropic', 'google', 'mistral', 'custom']

        for provider in preferred_order:
            if provider in available_providers:
                self.fallback_providers.append(provider)

    async def process_with_fallback(self, operation: str, *args, **kwargs) -> Any:
        """Execute operation with fallback providers."""
        primary_provider = kwargs.get('provider', self.settings.default_ai_provider)

        # Try primary provider first
        if primary_provider in self.processor.providers:
            try:
                self.logger.info(f"Trying primary provider: {primary_provider}")
                # Ensure we use the primary provider
                kwargs['provider'] = primary_provider

                if operation == 'identify_bank':
                    return await self.processor.identify_bank(*args, **kwargs)
                elif operation == 'extract_transactions':
                    return await self.processor.extract_transactions(*args, **kwargs)
                else:
                    raise ValueError(f"Unknown operation: {operation}")

            except Exception as e:
                self.logger.warning(f"Primary provider {primary_provider} failed: {e}")

        # Try fallback providers
        for fallback_provider in self.fallback_providers:
            try:
                self.logger.info(f"Trying fallback provider: {fallback_provider}")
                kwargs['provider'] = fallback_provider

                if operation == 'identify_bank':
                    return await self.processor.identify_bank(*args, **kwargs)
                elif operation == 'extract_transactions':
                    return await self.processor.extract_transactions(*args, **kwargs)

            except Exception as e:
                self.logger.warning(f"Fallback provider {fallback_provider} failed: {e}")
                continue

        # All providers failed
        self.logger.error(f"All providers failed for operation: {operation}")
        if operation == 'identify_bank':
            return {
                'bank_name': 'Unknown',
                'country': 'Unknown',
                'format_type': 'Unknown',
                'language': 'Unknown',
                'confidence': 0.0
            }
        elif operation == 'extract_transactions':
            return TransactionBatch(
                transactions=[],
                processed_at=datetime.now(),
                ai_provider='failed',
                confidence_score=0.0
            )

    async def identify_bank_with_fallback(self, statement_text: str, provider: str = None) -> Dict[str, Any]:
        """Identify bank with fallback support."""
        return await self.process_with_fallback('identify_bank', statement_text, provider=provider)

    async def extract_transactions_with_fallback(
        self,
        statement_text: str,
        bank_config: Optional[BankConfig] = None,
        provider: str = None
    ) -> TransactionBatch:
        """Extract transactions with fallback support."""
        return await self.process_with_fallback(
            'extract_transactions',
            statement_text,
            bank_config=bank_config,
            provider=provider
        )

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics."""
        return self.processor.get_usage_stats()

    def reset_usage_stats(self):
        """Reset usage statistics."""
        self.processor.reset_usage_stats()


# Global AI processor manager instance
ai_processor_manager = AIProcessorManager()


async def identify_bank(statement_text: str, provider: str = None) -> Dict[str, Any]:
    """Convenience function to identify bank."""
    return await ai_processor_manager.identify_bank_with_fallback(statement_text, provider)


async def extract_transactions(
    statement_text: str,
    bank_config: Optional[BankConfig] = None,
    provider: str = None
) -> TransactionBatch:
    """Convenience function to extract transactions."""
    return await ai_processor_manager.extract_transactions_with_fallback(
        statement_text, bank_config, provider
    )
