"""
Bank-specific configuration models for statement processing.
"""

from typing import Dict, List, Optional, Pattern, Any
from pydantic import BaseModel, Field, validator
from enum import Enum
import re


class StatementFormat(str, Enum):
    """Statement format types."""
    PDF = "pdf"
    CSV = "csv"
    EXCEL = "excel"
    TXT = "txt"
    IMAGE = "image"


class DateFormat(str, Enum):
    """Common date formats."""
    DD_MM_YYYY = "DD/MM/YYYY"
    MM_DD_YYYY = "MM/DD/YYYY"
    YYYY_MM_DD = "YYYY-MM-DD"
    DD_MMM_YYYY = "DD-MMM-YYYY"
    MMM_DD_YYYY = "MMM DD, YYYY"


class CurrencyFormat(str, Enum):
    """Currency format types."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    INR = "INR"
    CAD = "CAD"
    AUD = "AUD"


class BankPattern(BaseModel):
    """Pattern matching configuration for bank statement parsing."""
    
    name: str = Field(..., description="Pattern name")
    regex: str = Field(..., description="Regular expression pattern")
    flags: int = Field(re.IGNORECASE, description="Regex flags")
    group_mapping: Dict[str, int] = Field(default_factory=dict, description="Mapping of field names to regex groups")
    
    @validator('regex')
    def validate_regex(cls, v):
        """Validate that regex pattern is valid."""
        try:
            re.compile(v)
            return v
        except re.error as e:
            raise ValueError(f"Invalid regex pattern: {e}")
    
    def compile(self) -> Pattern:
        """Compile the regex pattern."""
        return re.compile(self.regex, self.flags)
    
    def match(self, text: str) -> Optional[Dict[str, str]]:
        """Match pattern against text and return mapped groups."""
        pattern = self.compile()
        match = pattern.search(text)
        
        if not match:
            return None
        
        result = {}
        for field_name, group_index in self.group_mapping.items():
            try:
                result[field_name] = match.group(group_index)
            except IndexError:
                result[field_name] = None
        
        return result


class BankConfig(BaseModel):
    """Configuration for a specific bank's statement format."""
    
    # Bank identification
    bank_name: str = Field(..., description="Bank name")
    country: str = Field(..., description="Country code")
    bank_code: Optional[str] = Field(None, description="Bank code or identifier")
    
    # Format configuration
    supported_formats: List[StatementFormat] = Field(default_factory=list)
    default_format: StatementFormat = Field(StatementFormat.PDF)
    date_format: DateFormat = Field(DateFormat.DD_MM_YYYY)
    currency: CurrencyFormat = Field(CurrencyFormat.USD)
    
    # Identification patterns
    identification_patterns: List[BankPattern] = Field(default_factory=list)
    
    # Transaction parsing patterns
    transaction_patterns: List[BankPattern] = Field(default_factory=list)
    
    # Header and footer patterns (to ignore)
    header_patterns: List[str] = Field(default_factory=list)
    footer_patterns: List[str] = Field(default_factory=list)
    ignore_patterns: List[str] = Field(default_factory=list)
    
    # Column mappings for CSV/Excel
    csv_column_mapping: Dict[str, str] = Field(default_factory=dict)
    excel_sheet_name: Optional[str] = Field(None)
    
    # Balance and summary patterns
    opening_balance_pattern: Optional[BankPattern] = None
    closing_balance_pattern: Optional[BankPattern] = None
    
    # Validation rules
    min_transaction_amount: float = Field(0.01)
    max_transaction_amount: float = Field(1000000.0)
    required_fields: List[str] = Field(default=["date", "description"])
    
    # Processing hints
    multiline_transactions: bool = Field(False)
    decimal_separator: str = Field(".")
    thousands_separator: str = Field(",")
    
    class Config:
        use_enum_values = True
    
    def matches_bank(self, text: str) -> bool:
        """Check if text matches this bank's identification patterns."""
        for pattern in self.identification_patterns:
            if pattern.match(text):
                return True
        return False
    
    def extract_transactions(self, text: str) -> List[Dict[str, str]]:
        """Extract transaction data using configured patterns."""
        transactions = []
        
        for pattern in self.transaction_patterns:
            matches = pattern.compile().finditer(text)
            for match in matches:
                transaction_data = {}
                for field_name, group_index in pattern.group_mapping.items():
                    try:
                        transaction_data[field_name] = match.group(group_index)
                    except IndexError:
                        transaction_data[field_name] = None
                
                if self._is_valid_transaction(transaction_data):
                    transactions.append(transaction_data)
        
        return transactions
    
    def _is_valid_transaction(self, transaction_data: Dict[str, str]) -> bool:
        """Validate extracted transaction data."""
        # Check required fields
        for field in self.required_fields:
            if not transaction_data.get(field):
                return False
        
        # Check if it matches ignore patterns
        description = transaction_data.get('description', '')
        for ignore_pattern in self.ignore_patterns:
            if re.search(ignore_pattern, description, re.IGNORECASE):
                return False
        
        return True
    
    def clean_text(self, text: str) -> str:
        """Clean text by removing headers, footers, and noise."""
        cleaned = text
        
        # Remove header patterns
        for pattern in self.header_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.MULTILINE)
        
        # Remove footer patterns
        for pattern in self.footer_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.MULTILINE)
        
        return cleaned.strip()


class BankRegistry(BaseModel):
    """Registry of bank configurations."""
    
    banks: Dict[str, BankConfig] = Field(default_factory=dict)
    
    def add_bank(self, bank_config: BankConfig):
        """Add a bank configuration to the registry."""
        key = f"{bank_config.bank_name}_{bank_config.country}".lower()
        self.banks[key] = bank_config
    
    def identify_bank(self, text: str) -> Optional[BankConfig]:
        """Identify bank from statement text."""
        for bank_config in self.banks.values():
            if bank_config.matches_bank(text):
                return bank_config
        return None
    
    def get_bank(self, bank_name: str, country: str = None) -> Optional[BankConfig]:
        """Get bank configuration by name and country."""
        if country:
            key = f"{bank_name}_{country}".lower()
            return self.banks.get(key)
        
        # Search by name only
        for key, bank_config in self.banks.items():
            if bank_config.bank_name.lower() == bank_name.lower():
                return bank_config
        
        return None
    
    def list_banks(self) -> List[str]:
        """List all registered banks."""
        return [f"{config.bank_name} ({config.country})" for config in self.banks.values()]


# Default bank configurations
def create_default_bank_configs() -> BankRegistry:
    """Create default bank configurations for common banks."""
    registry = BankRegistry()
    
    # Example: Chase Bank (US)
    chase_config = BankConfig(
        bank_name="Chase Bank",
        country="US",
        bank_code="CHASE",
        supported_formats=[StatementFormat.PDF, StatementFormat.CSV],
        date_format=DateFormat.MM_DD_YYYY,
        currency=CurrencyFormat.USD,
        identification_patterns=[
            BankPattern(
                name="chase_header",
                regex=r"JPMorgan Chase Bank|Chase.*Statement",
                group_mapping={}
            )
        ],
        transaction_patterns=[
            BankPattern(
                name="chase_transaction",
                regex=r"(\d{2}/\d{2}/\d{4})\s+(.+?)\s+(-?\$?[\d,]+\.?\d*)",
                group_mapping={"date": 1, "description": 2, "amount": 3}
            )
        ],
        header_patterns=[
            r"Account Summary",
            r"Previous Balance",
            r"Statement Period"
        ],
        ignore_patterns=[
            r"BEGINNING BALANCE",
            r"ENDING BALANCE",
            r"Page \d+ of \d+"
        ]
    )
    registry.add_bank(chase_config)
    
    # Example: Bank of America (US)
    boa_config = BankConfig(
        bank_name="Bank of America",
        country="US",
        bank_code="BOA",
        supported_formats=[StatementFormat.PDF, StatementFormat.CSV],
        date_format=DateFormat.MM_DD_YYYY,
        currency=CurrencyFormat.USD,
        identification_patterns=[
            BankPattern(
                name="boa_header",
                regex=r"Bank of America|BofA.*Statement",
                group_mapping={}
            )
        ]
    )
    registry.add_bank(boa_config)
    
    return registry


# Global bank registry instance
default_bank_registry = create_default_bank_configs()
