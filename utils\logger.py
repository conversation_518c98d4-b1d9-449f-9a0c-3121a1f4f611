"""
Logging utilities for bank statement processor.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json
import traceback
from contextlib import contextmanager


class ColoredFormatter(logging.Formatter):
    """Custom formatter with color support for console output."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry)


class LoggerManager:
    """Manager for application logging configuration."""
    
    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.configured = False
    
    def setup_logging(
        self,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        log_max_size_mb: int = 10,
        log_backup_count: int = 5,
        console_output: bool = True,
        json_format: bool = False
    ):
        """Setup logging configuration."""
        
        # Convert string level to logging constant
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Create logs directory if needed
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            
            if json_format:
                console_formatter = JSONFormatter()
            else:
                console_formatter = ColoredFormatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
            
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # File handler
        if log_file:
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=log_max_size_mb * 1024 * 1024,
                backupCount=log_backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(numeric_level)
            
            if json_format:
                file_formatter = JSONFormatter()
            else:
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
            
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
        
        self.configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger with the given name."""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        
        return self.loggers[name]
    
    def log_performance(self, logger: logging.Logger, operation: str, duration: float, **kwargs):
        """Log performance metrics."""
        extra_data = {
            'operation': operation,
            'duration_seconds': duration,
            'performance_metric': True
        }
        extra_data.update(kwargs)
        
        logger.info(f"Performance: {operation} completed in {duration:.2f}s", extra=extra_data)
    
    def log_api_call(self, logger: logging.Logger, provider: str, model: str, 
                     tokens_used: int, cost: float, success: bool, **kwargs):
        """Log API call metrics."""
        extra_data = {
            'api_provider': provider,
            'api_model': model,
            'tokens_used': tokens_used,
            'cost_usd': cost,
            'api_success': success,
            'api_metric': True
        }
        extra_data.update(kwargs)
        
        status = "successful" if success else "failed"
        logger.info(f"API call to {provider}/{model}: {status}, tokens: {tokens_used}, cost: ${cost:.4f}", 
                   extra=extra_data)
    
    def log_file_processing(self, logger: logging.Logger, file_path: str, 
                           file_size: int, processing_time: float, success: bool, **kwargs):
        """Log file processing metrics."""
        extra_data = {
            'file_path': file_path,
            'file_size_bytes': file_size,
            'processing_time_seconds': processing_time,
            'processing_success': success,
            'file_processing_metric': True
        }
        extra_data.update(kwargs)
        
        status = "successful" if success else "failed"
        logger.info(f"File processing {status}: {file_path} ({file_size} bytes) in {processing_time:.2f}s", 
                   extra=extra_data)


@contextmanager
def log_execution_time(logger: logging.Logger, operation: str, **kwargs):
    """Context manager to log execution time of operations."""
    start_time = datetime.now()
    try:
        yield
        success = True
    except Exception as e:
        success = False
        logger.error(f"Error in {operation}: {str(e)}", exc_info=True)
        raise
    finally:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        extra_data = {
            'operation': operation,
            'duration_seconds': duration,
            'success': success,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }
        extra_data.update(kwargs)
        
        if success:
            logger.info(f"Operation '{operation}' completed successfully in {duration:.2f}s", extra=extra_data)
        else:
            logger.error(f"Operation '{operation}' failed after {duration:.2f}s", extra=extra_data)


class ProcessingLogger:
    """Specialized logger for processing operations."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.processing_stats = {
            'files_processed': 0,
            'files_failed': 0,
            'total_transactions': 0,
            'api_calls': 0,
            'total_cost': 0.0,
            'start_time': None,
            'end_time': None
        }
    
    def start_processing(self):
        """Mark start of processing session."""
        self.processing_stats['start_time'] = datetime.now()
        self.logger.info("Processing session started", extra={'session_start': True})
    
    def end_processing(self):
        """Mark end of processing session and log summary."""
        self.processing_stats['end_time'] = datetime.now()
        
        if self.processing_stats['start_time']:
            duration = (self.processing_stats['end_time'] - self.processing_stats['start_time']).total_seconds()
            self.processing_stats['total_duration'] = duration
        
        self.logger.info("Processing session completed", extra={
            'session_end': True,
            'processing_summary': self.processing_stats
        })
    
    def log_file_start(self, file_path: str):
        """Log start of file processing."""
        self.logger.info(f"Starting processing: {file_path}", extra={'file_start': file_path})
    
    def log_file_success(self, file_path: str, transaction_count: int, processing_time: float):
        """Log successful file processing."""
        self.processing_stats['files_processed'] += 1
        self.processing_stats['total_transactions'] += transaction_count
        
        self.logger.info(f"Successfully processed: {file_path} ({transaction_count} transactions)", 
                        extra={
                            'file_success': file_path,
                            'transaction_count': transaction_count,
                            'processing_time': processing_time
                        })
    
    def log_file_error(self, file_path: str, error: str):
        """Log file processing error."""
        self.processing_stats['files_failed'] += 1
        
        self.logger.error(f"Failed to process: {file_path} - {error}", 
                         extra={'file_error': file_path, 'error_message': error})
    
    def log_api_usage(self, provider: str, tokens: int, cost: float):
        """Log API usage."""
        self.processing_stats['api_calls'] += 1
        self.processing_stats['total_cost'] += cost
        
        self.logger.debug(f"API usage: {provider} - {tokens} tokens, ${cost:.4f}", 
                         extra={
                             'api_usage': True,
                             'provider': provider,
                             'tokens': tokens,
                             'cost': cost
                         })


# Global logger manager
logger_manager = LoggerManager()


def setup_logging(**kwargs):
    """Setup global logging configuration."""
    logger_manager.setup_logging(**kwargs)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance."""
    return logger_manager.get_logger(name)


def get_processing_logger(name: str) -> ProcessingLogger:
    """Get a processing logger instance."""
    logger = get_logger(name)
    return ProcessingLogger(logger)
