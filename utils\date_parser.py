"""
Date parsing utilities for bank statement processor.
"""

import re
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from dateutil import parser as dateutil_parser
from dateutil.relativedelta import relativedelta


class DateParser:
    """Utility class for parsing dates from various formats."""
    
    def __init__(self):
        # Common date patterns with their regex and format strings
        self.date_patterns = [
            # DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY
            {
                'regex': r'\b(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})\b',
                'format': '%d/%m/%Y',
                'groups': [1, 2, 3],  # day, month, year
                'name': 'DD/MM/YYYY'
            },
            # MM/DD/YYYY, MM-DD-YYYY, MM.DD.YYYY
            {
                'regex': r'\b(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})\b',
                'format': '%m/%d/%Y',
                'groups': [2, 1, 3],  # month, day, year (US format)
                'name': 'MM/DD/YYYY'
            },
            # YYYY/MM/DD, YYYY-MM-DD, YYYY.MM.DD
            {
                'regex': r'\b(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})\b',
                'format': '%Y/%m/%d',
                'groups': [3, 2, 1],  # year, month, day
                'name': 'YYYY/MM/DD'
            },
            # DD MMM YYYY, DD-MMM-YYYY (e.g., 15 Jan 2024, 15-Jan-2024)
            {
                'regex': r'\b(\d{1,2})[\s\-]([A-Za-z]{3})[\s\-](\d{4})\b',
                'format': '%d %b %Y',
                'groups': [1, 2, 3],
                'name': 'DD MMM YYYY'
            },
            # MMM DD, YYYY (e.g., Jan 15, 2024)
            {
                'regex': r'\b([A-Za-z]{3})\s+(\d{1,2}),?\s+(\d{4})\b',
                'format': '%b %d %Y',
                'groups': [2, 1, 3],
                'name': 'MMM DD, YYYY'
            },
            # DD/MM/YY, DD-MM-YY
            {
                'regex': r'\b(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})\b',
                'format': '%d/%m/%y',
                'groups': [1, 2, 3],
                'name': 'DD/MM/YY'
            },
            # YYYYMMDD
            {
                'regex': r'\b(\d{4})(\d{2})(\d{2})\b',
                'format': '%Y%m%d',
                'groups': [3, 2, 1],
                'name': 'YYYYMMDD'
            }
        ]
        
        # Month name mappings
        self.month_names = {
            'jan': 1, 'january': 1,
            'feb': 2, 'february': 2,
            'mar': 3, 'march': 3,
            'apr': 4, 'april': 4,
            'may': 5,
            'jun': 6, 'june': 6,
            'jul': 7, 'july': 7,
            'aug': 8, 'august': 8,
            'sep': 9, 'september': 9,
            'oct': 10, 'october': 10,
            'nov': 11, 'november': 11,
            'dec': 12, 'december': 12
        }
    
    def parse_date(self, date_string: str, preferred_format: str = None) -> Optional[date]:
        """Parse date string using various formats."""
        if not date_string or not isinstance(date_string, str):
            return None
        
        # Clean the date string
        cleaned = self._clean_date_string(date_string)
        
        # Try preferred format first if specified
        if preferred_format:
            result = self._try_format(cleaned, preferred_format)
            if result:
                return result
        
        # Try all patterns
        for pattern in self.date_patterns:
            result = self._try_pattern(cleaned, pattern)
            if result:
                return result
        
        # Try dateutil parser as fallback
        try:
            parsed = dateutil_parser.parse(cleaned, fuzzy=True)
            return parsed.date()
        except:
            pass
        
        return None
    
    def _clean_date_string(self, date_string: str) -> str:
        """Clean date string by removing extra characters."""
        # Remove common prefixes/suffixes
        cleaned = re.sub(r'^(date|on|from|to)[\s:]*', '', date_string, flags=re.IGNORECASE)
        cleaned = re.sub(r'[\s:]*$', '', cleaned)
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    def _try_pattern(self, date_string: str, pattern: Dict[str, Any]) -> Optional[date]:
        """Try to parse date using a specific pattern."""
        try:
            match = re.search(pattern['regex'], date_string)
            if not match:
                return None
            
            groups = match.groups()
            if len(groups) < 3:
                return None
            
            # Extract day, month, year based on pattern groups
            day_idx, month_idx, year_idx = pattern['groups']
            day = int(groups[day_idx - 1])
            year = int(groups[year_idx - 1])
            
            # Handle month (could be number or name)
            month_str = groups[month_idx - 1]
            if month_str.isdigit():
                month = int(month_str)
            else:
                month = self.month_names.get(month_str.lower())
                if not month:
                    return None
            
            # Handle 2-digit years
            if year < 100:
                current_year = datetime.now().year
                if year > (current_year % 100):
                    year += 1900
                else:
                    year += 2000
            
            # Validate ranges
            if not (1 <= month <= 12):
                return None
            if not (1 <= day <= 31):
                return None
            if not (1900 <= year <= 2100):
                return None
            
            return date(year, month, day)
        
        except (ValueError, IndexError):
            return None
    
    def _try_format(self, date_string: str, format_string: str) -> Optional[date]:
        """Try to parse date using a specific format string."""
        try:
            parsed = datetime.strptime(date_string, format_string)
            return parsed.date()
        except ValueError:
            return None
    
    def detect_date_format(self, date_strings: List[str]) -> Optional[str]:
        """Detect the most likely date format from a list of date strings."""
        format_scores = {}
        
        for date_string in date_strings[:10]:  # Test first 10 dates
            if not date_string:
                continue
            
            cleaned = self._clean_date_string(date_string)
            
            for pattern in self.date_patterns:
                if self._try_pattern(cleaned, pattern):
                    format_name = pattern['name']
                    format_scores[format_name] = format_scores.get(format_name, 0) + 1
        
        if not format_scores:
            return None
        
        # Return the format with highest score
        return max(format_scores, key=format_scores.get)
    
    def normalize_date(self, date_obj: date, output_format: str = '%d/%m/%Y') -> str:
        """Normalize date to a standard format."""
        if not date_obj:
            return ""
        
        return date_obj.strftime(output_format)
    
    def parse_date_range(self, text: str) -> Optional[Dict[str, date]]:
        """Parse date range from text (e.g., 'from 01/01/2024 to 31/01/2024')."""
        # Common date range patterns
        range_patterns = [
            r'from\s+(.+?)\s+to\s+(.+?)(?:\s|$)',
            r'between\s+(.+?)\s+and\s+(.+?)(?:\s|$)',
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s*[-–—]\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'period:\s*(.+?)\s*[-–—]\s*(.+?)(?:\s|$)'
        ]
        
        for pattern in range_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                start_str, end_str = match.groups()
                start_date = self.parse_date(start_str.strip())
                end_date = self.parse_date(end_str.strip())
                
                if start_date and end_date:
                    return {
                        'start': start_date,
                        'end': end_date
                    }
        
        return None
    
    def get_month_year(self, date_obj: date) -> str:
        """Get month-year string from date (e.g., '2024-01')."""
        if not date_obj:
            return ""
        
        return date_obj.strftime('%Y-%m')
    
    def get_month_name(self, date_obj: date) -> str:
        """Get month name from date (e.g., 'January 2024')."""
        if not date_obj:
            return ""
        
        return date_obj.strftime('%B %Y')
    
    def is_valid_date_range(self, start_date: date, end_date: date, max_days: int = 366) -> bool:
        """Validate if date range is reasonable."""
        if not start_date or not end_date:
            return False
        
        if start_date > end_date:
            return False
        
        delta = end_date - start_date
        return delta.days <= max_days
    
    def get_financial_year(self, date_obj: date, fy_start_month: int = 4) -> str:
        """Get financial year for a date (default: April start)."""
        if not date_obj:
            return ""
        
        if date_obj.month >= fy_start_month:
            return f"{date_obj.year}-{date_obj.year + 1}"
        else:
            return f"{date_obj.year - 1}-{date_obj.year}"
    
    def get_quarter(self, date_obj: date) -> str:
        """Get quarter for a date (Q1, Q2, Q3, Q4)."""
        if not date_obj:
            return ""
        
        quarter = (date_obj.month - 1) // 3 + 1
        return f"Q{quarter} {date_obj.year}"


# Global date parser instance
date_parser = DateParser()


def parse_date(date_string: str, preferred_format: str = None) -> Optional[date]:
    """Convenience function to parse a date string."""
    return date_parser.parse_date(date_string, preferred_format)


def normalize_date(date_obj: date, output_format: str = '%d/%m/%Y') -> str:
    """Convenience function to normalize a date."""
    return date_parser.normalize_date(date_obj, output_format)
