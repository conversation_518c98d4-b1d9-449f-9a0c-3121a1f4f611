# AI-Powered Bank Statement Processor

A comprehensive Python application that processes bank statements using AI APIs to extract transaction data and generate organized Excel reports.

## 🚀 Features

- **Multi-AI Provider Support**: OpenAI, <PERSON>, <PERSON>, Mistral, DeepSeek, OpenRouter, and custom LLMs
- **Multiple File Formats**: PDF, CSV, Excel, TXT, and images (JPG, PNG) with OCR support
- **Intelligent Processing**: AI-powered transaction extraction and data cleaning
- **Organized Output**: Separate Excel workbooks for each bank with monthly sheets
- **Robust Error Handling**: Comprehensive error management and retry mechanisms
- **Progress Tracking**: Real-time processing status and progress bars
- **Interactive CLI**: User-friendly command-line interface with rich formatting
- **Batch Processing**: Process multiple files simultaneously
- **Configurable**: Flexible configuration system with environment variables
- **Logging & Monitoring**: Detailed logging and processing statistics
- **Export Options**: Excel, CSV, and JSON export formats

## 📋 Requirements

- Python 3.8 or higher
- Tesseract OCR (for image processing)
- API keys for at least one AI provider

## 🛠️ Installation

### 1. Clone or Download
```bash
# If you have the source code
cd bank_statement_processor
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Install Tesseract OCR (for image processing)

**Windows:**
- Download from: https://github.com/UB-Mannheim/tesseract/wiki
- Add to PATH

**macOS:**
```bash
brew install tesseract
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install tesseract-ocr
```

### 4. Configuration
```bash
cp .env.example .env
```

Edit the `.env` file with your API keys and preferences:
```env
# Required: At least one AI provider API key
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Set your preferred default provider
DEFAULT_AI_PROVIDER=openai

# Optional: Adjust other settings as needed
AI_TEMPERATURE=0.1
MAX_FILE_SIZE_MB=50
ENABLE_OCR=true
```

## 🚀 Quick Start

### Interactive Mode (Recommended)
```bash
python main.py
```

This launches the interactive CLI with the following options:
1. Process all statements
2. Process specific bank statements
3. Configure AI providers
4. View processing history
5. Generate reports
6. Settings
7. Exit

### Batch Mode
```bash
# Process all files in statements directory
python main.py --batch

# Specify custom directories
python main.py --batch --input-dir /path/to/statements --output-dir /path/to/output
```

## 📁 Project Structure

```
bank_statement_processor/
├── main.py                     # Main CLI application
├── config/
│   ├── settings.py            # Configuration management
│   └── llm_config.py          # AI provider configurations
├── processors/
│   ├── statement_reader.py    # File reading and parsing
│   ├── ai_processor.py        # AI integration with fallback
│   └── excel_generator.py     # Excel file generation
├── utils/
│   ├── file_handler.py        # File operations and validation
│   ├── date_parser.py         # Date parsing utilities
│   ├── logger.py              # Logging system
│   └── error_handler.py       # Error handling and recovery
├── models/
│   ├── transaction.py         # Transaction data models
│   └── bank_config.py         # Bank-specific configurations
├── statements/                # Input folder (create this)
├── processed/                 # Processed files archive
├── output/                    # Generated Excel files
├── logs/                      # Application logs
├── requirements.txt
├── .env.example
└── README.md
```

## 🤖 Supported AI Providers

| Provider | Models | Setup |
|----------|--------|-------|
| **OpenAI** | GPT-4, GPT-3.5-turbo | Get API key from [OpenAI](https://platform.openai.com/) |
| **Anthropic** | Claude-3 Sonnet, Haiku | Get API key from [Anthropic](https://console.anthropic.com/) |
| **Google** | Gemini Pro | Get API key from [Google AI Studio](https://makersuite.google.com/) |
| **Mistral** | Mistral Large, Medium | Get API key from [Mistral](https://console.mistral.ai/) |
| **DeepSeek** | DeepSeek Chat | Get API key from [DeepSeek](https://platform.deepseek.com/) |
| **OpenRouter** | Multiple models | Get API key from [OpenRouter](https://openrouter.ai/) |
| **Custom** | Any OpenAI-compatible API | Set `CUSTOM_LLM_ENDPOINT` and `CUSTOM_LLM_API_KEY` |

## 📄 Supported File Formats

| Format | Extension | Notes |
|--------|-----------|-------|
| **PDF** | `.pdf` | Text extraction using PyPDF2 |
| **CSV** | `.csv` | Automatic delimiter detection |
| **Excel** | `.xlsx`, `.xls` | Multiple sheets supported |
| **Text** | `.txt` | Plain text files |
| **Images** | `.jpg`, `.jpeg`, `.png` | OCR using Tesseract |

## 💡 Usage Examples

### Basic Usage
1. Place your bank statements in the `statements/` folder
2. Run `python main.py`
3. Select "Process all statements"
4. Find generated Excel files in the `output/` folder

### Processing Specific Files
1. Run `python main.py`
2. Select "Process specific bank statements"
3. Choose files from the list
4. Review results in the output folder

### Batch Processing
```bash
# Process all files automatically
python main.py --batch

# Custom input/output directories
python main.py --batch -i /path/to/statements -o /path/to/output
```

## ⚙️ Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DEFAULT_AI_PROVIDER` | `openai` | Primary AI provider to use |
| `AI_TEMPERATURE` | `0.1` | AI response randomness (0.0-1.0) |
| `AI_MAX_TOKENS` | `4000` | Maximum tokens per AI request |
| `MAX_FILE_SIZE_MB` | `50` | Maximum file size to process |
| `ENABLE_OCR` | `true` | Enable OCR for image files |
| `SEPARATE_BANKS` | `true` | Create separate Excel files per bank |
| `MONTHLY_SHEETS` | `true` | Create separate sheets per month |
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |

### Advanced Configuration

Create a `config.yaml` file for advanced settings:
```yaml
ai_providers:
  openai:
    model: "gpt-4"
    temperature: 0.1
    max_tokens: 4000

  anthropic:
    model: "claude-3-sonnet-********"
    temperature: 0.1

processing:
  batch_size: 10
  max_concurrent_files: 3
  retry_attempts: 3
  retry_delay: 1

output:
  formats: ["xlsx", "csv"]
  include_summary: true
  date_format: "DD/MM/YYYY"
```

## 📊 Output Formats

### Excel Workbooks
- **Separate Banks**: One workbook per bank (default)
- **Monthly Sheets**: Separate sheet for each month
- **Summary Sheet**: Overview with totals and statistics
- **Formatting**: Color-coded debits (red) and credits (green)

### CSV Export
- Standard comma-separated format
- All transactions in single file
- Compatible with Excel and other tools

### JSON Export
- Structured data with metadata
- Includes processing information
- Suitable for further data analysis

## 🔧 Troubleshooting

### Common Issues

**1. No AI providers configured**
```
Error: No providers configured
Solution: Add at least one API key to .env file
```

**2. Tesseract not found (for OCR)**
```
Error: TesseractNotFoundError
Solution: Install Tesseract OCR and add to PATH
```

**3. File too large**
```
Error: File too large
Solution: Increase MAX_FILE_SIZE_MB in .env or split the file
```

**4. API rate limits**
```
Error: Rate limit exceeded
Solution: The app automatically retries with exponential backoff
```

### Debug Mode
Enable debug logging for troubleshooting:
```env
LOG_LEVEL=DEBUG
```

### Log Files
Check log files in the `logs/` directory:
- `app.log` - Main application log
- Rotated logs with timestamps

## 🎯 Best Practices

### File Organization
```
statements/
├── bank1/
│   ├── 2024-01-statement.pdf
│   └── 2024-02-statement.pdf
├── bank2/
│   ├── january-2024.csv
│   └── february-2024.csv
└── mixed-statements/
    ├── statement1.pdf
    └── statement2.xlsx
```

### AI Provider Selection
- **OpenAI GPT-4**: Best accuracy, higher cost
- **Claude-3 Sonnet**: Good balance of accuracy and cost
- **Gemini Pro**: Fast and cost-effective
- **Mistral**: Good for European banks
- **DeepSeek**: Very cost-effective option

### Performance Tips
1. **Batch Processing**: Process multiple files together
2. **File Size**: Keep files under 50MB for best performance
3. **OCR**: Disable OCR if not processing images
4. **Concurrent Processing**: Adjust `MAX_CONCURRENT_FILES` based on your system

## 🔒 Security & Privacy

### Data Security
- **Local Processing**: All data processed locally
- **No Data Storage**: AI providers don't store your data
- **Secure API**: All API calls use HTTPS
- **File Cleanup**: Temporary files automatically cleaned

### API Key Security
- Store API keys in `.env` file (never commit to version control)
- Use environment variables in production
- Rotate API keys regularly
- Monitor API usage and costs

## 📈 Monitoring & Analytics

### Processing Statistics
The application tracks:
- Files processed successfully/failed
- Total transactions extracted
- AI API usage and costs
- Processing time per file
- Error rates by category

### Usage Reports
Access via the CLI menu:
1. View processing history
2. Generate reports
3. Export usage statistics

## 🤝 Contributing

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd bank_statement_processor

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install pytest black flake8 mypy
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=bank_statement_processor

# Run specific test file
pytest tests/test_transaction.py
```

### Code Style
```bash
# Format code
black bank_statement_processor/

# Check style
flake8 bank_statement_processor/

# Type checking
mypy bank_statement_processor/
```

## 🚀 Advanced Features

### Custom Bank Configurations
Add support for specific bank formats by creating custom configurations:

```python
from bank_statement_processor.models.bank_config import BankConfig, BankPattern

# Create custom bank configuration
custom_bank = BankConfig(
    bank_name="My Bank",
    country="US",
    supported_formats=["pdf", "csv"],
    identification_patterns=[
        BankPattern(
            name="header_pattern",
            regex=r"My Bank.*Statement",
            group_mapping={}
        )
    ],
    transaction_patterns=[
        BankPattern(
            name="transaction_pattern",
            regex=r"(\d{2}/\d{2}/\d{4})\s+(.+?)\s+([\d,]+\.?\d*)",
            group_mapping={"date": 1, "description": 2, "amount": 3}
        )
    ]
)
```

### API Integration
Use the processor programmatically:

```python
import asyncio
from pathlib import Path
from bank_statement_processor.processors.statement_reader import read_statement
from bank_statement_processor.processors.ai_processor import extract_transactions
from bank_statement_processor.processors.excel_generator import generate_excel_workbook

async def process_statement(file_path):
    # Read statement
    result = read_statement(file_path)
    if not result['success']:
        return None

    # Extract transactions
    batch = await extract_transactions(result['text'])

    # Generate Excel
    output_path = Path("output")
    excel_result = generate_excel_workbook([batch], output_path)

    return excel_result

# Usage
asyncio.run(process_statement("statement.pdf"))
```

### Webhook Integration
Set up webhooks for automated processing:

```python
from flask import Flask, request
import asyncio

app = Flask(__name__)

@app.route('/process', methods=['POST'])
def process_webhook():
    file_data = request.files['statement']
    # Process file and return results
    return {"status": "processed"}
```

## 📚 API Reference

### Core Classes

#### `Transaction`
Represents a single bank transaction.

```python
from bank_statement_processor.models.transaction import Transaction
from datetime import date
from decimal import Decimal

transaction = Transaction(
    date=date(2024, 1, 15),
    description="ATM Withdrawal",
    debit=Decimal("100.00"),
    credit=None,
    balance=Decimal("1500.00")
)
```

#### `TransactionBatch`
Collection of transactions from a single statement.

```python
from bank_statement_processor.models.transaction import TransactionBatch

batch = TransactionBatch(
    transactions=[transaction1, transaction2],
    bank_name="My Bank",
    statement_period="January 2024"
)
```

#### `AIProcessor`
Handles AI-powered transaction extraction.

```python
from bank_statement_processor.processors.ai_processor import AIProcessor

processor = AIProcessor()
batch = await processor.extract_transactions(statement_text)
```

### Configuration Classes

#### `Settings`
Main configuration class using Pydantic.

```python
from bank_statement_processor.config.settings import get_settings

settings = get_settings()
print(settings.default_ai_provider)
```

## 🔍 Examples

### Example 1: Basic Processing
```python
import asyncio
from pathlib import Path
from bank_statement_processor.main import BankStatementProcessor

async def main():
    processor = BankStatementProcessor()
    await processor.process_all_statements()

asyncio.run(main())
```

### Example 2: Custom Processing Pipeline
```python
import asyncio
from bank_statement_processor.processors import (
    statement_reader, ai_processor_manager, excel_generator
)

async def custom_pipeline(file_path):
    # Step 1: Read file
    result = statement_reader.read_statement(file_path)

    # Step 2: Extract transactions
    batch = await ai_processor_manager.extract_transactions_with_fallback(
        result['text']
    )

    # Step 3: Generate output
    excel_result = excel_generator.generate_workbook(
        [batch],
        Path("custom_output")
    )

    return excel_result
```

### Example 3: Batch Processing with Custom Settings
```python
from bank_statement_processor.config.settings import get_settings
from pathlib import Path

# Override settings
settings = get_settings()
settings.separate_banks = False
settings.monthly_sheets = True

# Process files
files = list(Path("statements").glob("*.pdf"))
# ... processing logic
```

## 📋 Changelog

### Version 1.0.0 (Current)
- ✅ Multi-AI provider support
- ✅ Multiple file format support
- ✅ Interactive CLI interface
- ✅ Excel report generation
- ✅ Comprehensive error handling
- ✅ Logging and monitoring
- ✅ OCR support for images
- ✅ Batch processing
- ✅ Configuration management

### Planned Features
- 🔄 Transaction categorization
- 🔄 Spending analysis and insights
- 🔄 Budget tracking integration
- 🔄 Web interface
- 🔄 Database storage options
- 🔄 Email notifications
- 🔄 Scheduled processing
- 🔄 Multi-currency support

## ❓ FAQ

**Q: Which AI provider should I use?**
A: Start with OpenAI GPT-4 for best accuracy, or Claude-3 Sonnet for good balance of cost and performance.

**Q: Can I process statements in languages other than English?**
A: Yes, most AI providers support multiple languages. Set the OCR language in configuration.

**Q: How accurate is the transaction extraction?**
A: Accuracy varies by statement format and AI provider, typically 90-95% for well-formatted statements.

**Q: Can I add support for my bank's specific format?**
A: Yes, you can create custom bank configurations or request support for specific formats.

**Q: Is my financial data secure?**
A: Yes, all processing is done locally. AI providers process text but don't store your data.

**Q: Can I run this on a server?**
A: Yes, use batch mode for server deployment: `python main.py --batch`

## 📞 Support

- **Issues**: Report bugs and request features on GitHub
- **Documentation**: Check this README and inline code documentation
- **Community**: Join discussions in GitHub Discussions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI, Anthropic, Google, and other AI providers for their APIs
- The Python community for excellent libraries
- Contributors and users for feedback and improvements

---

**Made with ❤️ for better financial data management**
