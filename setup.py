"""
Setup script for Bank Statement Processor.

This script helps users set up the application for first use.
"""

import os
import sys
from pathlib import Path
import subprocess
import shutil


def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_step(step_num, description):
    """Print a formatted step."""
    print(f"\n[Step {step_num}] {description}")
    print("-" * 40)


def check_python_version():
    """Check if Python version is compatible."""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print_step(2, "Installing Dependencies")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        print("Installing Python packages...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Python dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies")
            print(f"Error: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def check_tesseract():
    """Check if Tesseract OCR is installed."""
    print_step(3, "Checking Tesseract OCR")
    
    try:
        result = subprocess.run(["tesseract", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ {version_line}")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️  Tesseract OCR not found")
    print("   This is required for processing image files (JPG, PNG)")
    print("   Installation instructions:")
    print("   • Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
    print("   • macOS: brew install tesseract")
    print("   • Linux: sudo apt-get install tesseract-ocr")
    print("   You can continue without OCR, but image processing will be disabled.")
    return False


def setup_configuration():
    """Set up configuration files."""
    print_step(4, "Setting up Configuration")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    if env_file.exists():
        print("⚠️  .env file already exists")
        response = input("   Overwrite existing .env file? (y/N): ").lower()
        if response != 'y':
            print("   Keeping existing .env file")
            return True
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("   Please edit .env file to add your API keys")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False


def create_directories():
    """Create required directories."""
    print_step(5, "Creating Directories")
    
    directories = [
        "statements",
        "processed", 
        "output",
        "logs"
    ]
    
    created = []
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                created.append(directory)
            except Exception as e:
                print(f"❌ Error creating directory {directory}: {e}")
                return False
    
    if created:
        print(f"✅ Created directories: {', '.join(created)}")
    else:
        print("✅ All directories already exist")
    
    return True


def show_next_steps():
    """Show next steps to the user."""
    print_step(6, "Next Steps")
    
    print("🎉 Setup completed successfully!")
    print("\nTo get started:")
    print("1. Edit the .env file and add your AI provider API keys:")
    print("   • OPENAI_API_KEY=your_key_here")
    print("   • ANTHROPIC_API_KEY=your_key_here")
    print("   • GOOGLE_API_KEY=your_key_here")
    print("   • etc.")
    
    print("\n2. Place your bank statement files in the 'statements' directory")
    print("   Supported formats: PDF, CSV, Excel, TXT, JPG, PNG")
    
    print("\n3. Run the application:")
    print("   python main.py")
    
    print("\n4. Or try the example script:")
    print("   python example_usage.py")
    
    print("\nFor help and documentation, see README.md")


def main():
    """Main setup function."""
    print_header("Bank Statement Processor Setup")
    print("This script will help you set up the application for first use.")
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ Please run this script from the bank_statement_processor directory")
        sys.exit(1)
    
    success = True
    
    # Step 1: Check Python version
    if not check_python_version():
        success = False
    
    # Step 2: Install dependencies
    if success and not install_dependencies():
        success = False
    
    # Step 3: Check Tesseract (optional)
    tesseract_available = check_tesseract()
    
    # Step 4: Setup configuration
    if success and not setup_configuration():
        success = False
    
    # Step 5: Create directories
    if success and not create_directories():
        success = False
    
    # Show results
    if success:
        show_next_steps()
        
        if not tesseract_available:
            print("\n⚠️  Note: OCR functionality will be limited without Tesseract")
            print("   You can still process PDF, CSV, Excel, and TXT files")
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
