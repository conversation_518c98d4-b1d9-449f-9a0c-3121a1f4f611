"""
Example usage of the Bank Statement Processor.

This script demonstrates how to use the processor programmatically.
"""

import asyncio
import sys
from pathlib import Path
from datetime import date
from decimal import Decimal

from config.settings import get_settings
from processors.statement_reader import statement_reader
from processors.ai_processor import ai_processor_manager
from processors.excel_generator import excel_generator
from models.transaction import Transaction, TransactionBatch
from utils.logger import setup_logging, get_logger


async def example_basic_processing():
    """Example: Basic file processing."""
    print("=== Basic Processing Example ===")
    
    # Setup logging
    setup_logging(log_level="INFO", console_output=True)
    logger = get_logger(__name__)
    
    # Check if statements directory exists
    statements_dir = Path("statements")
    if not statements_dir.exists():
        print(f"Creating statements directory: {statements_dir}")
        statements_dir.mkdir(exist_ok=True)
        print("Please place your bank statement files in the 'statements' directory and run again.")
        return
    
    # Find statement files
    statement_files = []
    for pattern in ["*.pdf", "*.csv", "*.xlsx", "*.txt", "*.jpg", "*.png"]:
        statement_files.extend(statements_dir.glob(pattern))
    
    if not statement_files:
        print("No statement files found in 'statements' directory.")
        print("Supported formats: PDF, CSV, Excel, TXT, JPG, PNG")
        return
    
    print(f"Found {len(statement_files)} statement file(s)")
    
    # Process each file
    transaction_batches = []
    
    for file_path in statement_files:
        print(f"\nProcessing: {file_path.name}")
        
        try:
            # Step 1: Read the file
            print("  Reading file...")
            read_result = statement_reader.read_statement(file_path)
            
            if not read_result['success']:
                print(f"  ❌ Failed to read file: {read_result['error']}")
                continue
            
            print(f"  ✅ Read {len(read_result['text'])} characters")
            
            # Step 2: Identify bank (optional)
            print("  Identifying bank...")
            bank_info = await ai_processor_manager.identify_bank_with_fallback(
                read_result['text']
            )
            print(f"  🏦 Bank: {bank_info.get('bank_name', 'Unknown')}")
            
            # Step 3: Extract transactions
            print("  Extracting transactions...")
            batch = await ai_processor_manager.extract_transactions_with_fallback(
                read_result['text']
            )
            
            # Set additional metadata
            batch.bank_name = bank_info.get('bank_name', 'Unknown')
            batch.source_file = str(file_path)
            
            transaction_batches.append(batch)
            print(f"  ✅ Extracted {len(batch.transactions)} transactions")
        
        except Exception as e:
            print(f"  ❌ Error processing {file_path.name}: {e}")
            logger.error(f"Error processing {file_path}: {e}")
    
    if not transaction_batches:
        print("\nNo transactions extracted from any files.")
        return
    
    # Step 4: Generate Excel output
    print(f"\nGenerating Excel reports...")
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    result = excel_generator.generate_workbook(
        transaction_batches,
        output_dir,
        separate_banks=True,
        monthly_sheets=True
    )
    
    if result['success']:
        print(f"✅ Generated {len(result['files_created'])} Excel file(s):")
        for file_path in result['files_created']:
            print(f"  📄 {file_path}")
    else:
        print(f"❌ Failed to generate Excel files: {result['error']}")
    
    # Display summary
    total_transactions = sum(len(batch.transactions) for batch in transaction_batches)
    print(f"\n📊 Summary:")
    print(f"  Files processed: {len(transaction_batches)}")
    print(f"  Total transactions: {total_transactions}")
    
    # AI usage stats
    usage_stats = ai_processor_manager.get_usage_stats()
    print(f"  AI requests: {usage_stats['total_requests']}")
    print(f"  Total cost: ${usage_stats['total_cost']:.4f}")


def example_create_sample_transaction():
    """Example: Create a sample transaction programmatically."""
    print("\n=== Sample Transaction Creation ===")
    
    # Create individual transactions
    transaction1 = Transaction(
        date=date(2024, 1, 15),
        description="ATM Withdrawal - Main Street",
        debit=Decimal("100.00"),
        credit=None,
        balance=Decimal("1500.00"),
        bank_name="Sample Bank"
    )
    
    transaction2 = Transaction(
        date=date(2024, 1, 16),
        description="Salary Deposit",
        debit=None,
        credit=Decimal("3000.00"),
        balance=Decimal("4500.00"),
        bank_name="Sample Bank"
    )
    
    # Create a transaction batch
    batch = TransactionBatch(
        transactions=[transaction1, transaction2],
        bank_name="Sample Bank",
        account_number="****1234",
        statement_period="January 2024"
    )
    
    # Calculate totals
    batch.calculate_totals()
    
    print(f"Created batch with {len(batch.transactions)} transactions")
    print(f"Total debits: ${batch.total_debits}")
    print(f"Total credits: ${batch.total_credits}")
    
    # Generate Excel from sample data
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    result = excel_generator.generate_workbook(
        [batch],
        output_dir / "sample_output.xlsx"
    )
    
    if result['success']:
        print(f"✅ Sample Excel file created: {result['files_created'][0]}")
    else:
        print(f"❌ Failed to create sample Excel: {result['error']}")


def example_configuration():
    """Example: Working with configuration."""
    print("\n=== Configuration Example ===")
    
    settings = get_settings()
    
    print("Current configuration:")
    print(f"  Default AI Provider: {settings.default_ai_provider}")
    print(f"  Max File Size: {settings.max_file_size_mb} MB")
    print(f"  Separate Banks: {settings.separate_banks}")
    print(f"  Monthly Sheets: {settings.monthly_sheets}")
    print(f"  OCR Enabled: {settings.enable_ocr}")
    print(f"  Log Level: {settings.log_level}")
    
    # Check available AI providers
    available_providers = ai_processor_manager.processor.get_available_providers()
    print(f"  Available AI Providers: {', '.join(available_providers) if available_providers else 'None configured'}")
    
    # Validate provider configurations
    if available_providers:
        print("\nProvider validation:")
        for provider in available_providers:
            is_valid = settings.validate_provider_config(provider)
            status = "✅" if is_valid else "❌"
            print(f"  {status} {provider}")


async def main():
    """Main example function."""
    print("🤖 Bank Statement Processor - Example Usage")
    print("=" * 50)
    
    # Example 1: Configuration
    example_configuration()
    
    # Example 2: Create sample transaction
    example_create_sample_transaction()
    
    # Example 3: Process actual files (if available)
    await example_basic_processing()
    
    print("\n✅ Examples completed!")
    print("\nNext steps:")
    print("1. Add your API keys to .env file")
    print("2. Place bank statements in 'statements' directory")
    print("3. Run: python main.py")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
