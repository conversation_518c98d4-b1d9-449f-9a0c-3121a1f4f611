2025-07-13 22:52:45 - __main__ - [32m<PERSON><PERSON><PERSON>[0m - start_processing:243 - Processing session started
2025-07-13 22:52:45 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.02s
2025-07-13 22:52:45 - processors.ai_processor - [32mINFO[0m - process_with_fallback:441 - Trying fallback provider: openrouter
2025-07-13 22:52:50 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 22:52:50 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 5.34s
2025-07-13 22:52:50 - processors.ai_processor - [32mINFO[0m - process_with_fallback:441 - Trying fallback provider: openrouter
2025-07-13 22:53:04 - processors.ai_processor - [32m<PERSON>F<PERSON>[0m - extract_transactions:156 - Extracted 7 transactions using openrouter
2025-07-13 22:53:04 - processors.ai_processor - [32m<PERSON><PERSON><PERSON>[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 14.36s
2025-07-13 22:53:04 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ february2025.PDF (7 transactions)
2025-07-13 22:53:04 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.05s
2025-07-13 22:53:04 - processors.ai_processor - [32mINFO[0m - process_with_fallback:441 - Trying fallback provider: openrouter
2025-07-13 22:53:08 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 22:53:08 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 3.67s
2025-07-13 22:53:08 - processors.ai_processor - [32mINFO[0m - process_with_fallback:441 - Trying fallback provider: openrouter
2025-07-13 22:53:23 - processors.ai_processor - [32mINFO[0m - extract_transactions:156 - Extracted 12 transactions using openrouter
2025-07-13 22:53:23 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 14.91s
2025-07-13 22:53:23 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ june2025.PDF (12 transactions)
2025-07-13 22:53:23 - processors.excel_generator - [32mINFO[0m - _generate_separate_bank_workbooks:117 - Created workbook for Emirates_NBD: output\Emirates_NBD_Statements.xlsx
2025-07-13 22:53:23 - processors.excel_generator - [32mINFO[0m - log_execution_time:220 - Operation 'generate_excel_workbook' completed successfully in 0.07s
2025-07-13 22:53:23 - __main__ - [32mINFO[0m - end_processing:253 - Processing session completed
2025-07-13 23:19:59 - __main__ - [32mINFO[0m - start_processing:243 - Processing session started
2025-07-13 23:19:59 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.08s
2025-07-13 23:19:59 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:20:03 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 23:20:03 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 4.37s
2025-07-13 23:20:03 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:20:25 - processors.ai_processor - [32mINFO[0m - extract_transactions:156 - Extracted 7 transactions using openrouter
2025-07-13 23:20:25 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 21.53s
2025-07-13 23:20:25 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ february2025.PDF (7 transactions)
2025-07-13 23:20:25 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.01s
2025-07-13 23:20:25 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:20:34 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 1 failed: Custom API error: 500 - {"error":{"message":"Internal Server Error","code":500}}
2025-07-13 23:20:42 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 23:20:42 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 16.71s
2025-07-13 23:20:42 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:21:06 - processors.ai_processor - [32mINFO[0m - extract_transactions:156 - Extracted 12 transactions using openrouter
2025-07-13 23:21:06 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 24.67s
2025-07-13 23:21:06 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ june2025.PDF (12 transactions)
2025-07-13 23:21:06 - processors.excel_generator - [32mINFO[0m - _generate_separate_bank_workbooks:117 - Created workbook for Emirates_NBD: output\Emirates_NBD_Statements.xlsx
2025-07-13 23:21:06 - processors.excel_generator - [32mINFO[0m - log_execution_time:220 - Operation 'generate_excel_workbook' completed successfully in 0.06s
2025-07-13 23:21:06 - __main__ - [32mINFO[0m - end_processing:253 - Processing session completed
2025-07-13 23:23:09 - __main__ - [32mINFO[0m - start_processing:243 - Processing session started
2025-07-13 23:23:09 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.02s
2025-07-13 23:23:09 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:23:14 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 23:23:14 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 4.88s
2025-07-13 23:23:14 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:23:31 - processors.ai_processor - [32mINFO[0m - extract_transactions:156 - Extracted 7 transactions using openrouter
2025-07-13 23:23:31 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 16.74s
2025-07-13 23:23:31 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ february2025.PDF (7 transactions)
2025-07-13 23:23:31 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.03s
2025-07-13 23:23:31 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:23:36 - processors.ai_processor - [32mINFO[0m - identify_bank:115 - Bank identified: Emirates NBD
2025-07-13 23:23:36 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openrouter' completed successfully in 5.15s
2025-07-13 23:23:36 - processors.ai_processor - [32mINFO[0m - process_with_fallback:454 - Trying fallback provider: openrouter
2025-07-13 23:24:06 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 1 failed: 
2025-07-13 23:24:34 - processors.ai_processor - [32mINFO[0m - extract_transactions:156 - Extracted 12 transactions using openrouter
2025-07-13 23:24:34 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openrouter' completed successfully in 58.50s
2025-07-13 23:24:34 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_ june2025.PDF (12 transactions)
2025-07-13 23:24:34 - processors.excel_generator - [32mINFO[0m - _generate_separate_bank_workbooks:117 - Created workbook for Emirates_NBD: output\Emirates_NBD_Statements.xlsx
2025-07-13 23:24:34 - processors.excel_generator - [32mINFO[0m - log_execution_time:220 - Operation 'generate_excel_workbook' completed successfully in 0.06s
2025-07-13 23:24:34 - __main__ - [32mINFO[0m - end_processing:253 - Processing session completed
