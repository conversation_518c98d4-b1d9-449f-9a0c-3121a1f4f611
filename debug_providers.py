#!/usr/bin/env python3
"""
Debug script to check AI provider initialization.
"""

import asyncio
from config.settings import get_settings
from processors.ai_processor import ai_processor_manager

def main():
    """Debug AI providers."""
    print("=== AI Provider Debug ===")
    
    # Check settings
    settings = get_settings()
    print(f"Default AI Provider: {settings.default_ai_provider}")
    print(f"DeepSeek API Key: {'***' + settings.deepseek_api_key[-4:] if settings.deepseek_api_key else 'None'}")
    print(f"OpenRouter API Key: {'***' + settings.openrouter_api_key[-4:] if settings.openrouter_api_key else 'None'}")
    print(f"DeepSeek Model: {settings.deepseek_model}")
    print()
    
    # Check available providers
    available_providers = ai_processor_manager.processor.get_available_providers()
    print(f"Available Providers: {available_providers}")
    print(f"Fallback Providers: {ai_processor_manager.fallback_providers}")
    print()
    
    # Test provider validation
    for provider in ['deepseek', 'openrouter', 'openai', 'anthropic']:
        is_valid = settings.validate_provider_config(provider)
        api_key = settings.get_api_key(provider)
        print(f"{provider}: Valid={is_valid}, Has API Key={bool(api_key)}")
    
    print("\n=== Testing Bank Identification ===")

    # Test with sample ADCB text
    sample_text = """
    ADCB
    Abu Dhabi Commercial Bank
    Account Statement
    Account Number: *********
    Statement Period: 01/02/2025 to 28/02/2025
    """

    async def test_bank_identification():
        try:
            # Test with explicit DeepSeek provider
            print("Testing with explicit DeepSeek provider:")
            result = await ai_processor_manager.identify_bank_with_fallback(sample_text, provider='deepseek')
            print(f"DeepSeek Result: {result}")

            print("\nTesting with fallback (should use default):")
            result = await ai_processor_manager.identify_bank_with_fallback(sample_text)
            print(f"Fallback Result: {result}")

        except Exception as e:
            print(f"Error in bank identification: {e}")
            import traceback
            traceback.print_exc()

    async def test_transaction_extraction():
        print("\n=== Testing Transaction Extraction ===")

        # Sample transaction data with clear debit/credit examples
        transaction_text = """
        Date        Description                     Debit       Credit      Balance
        01/02/2025  SALARY TRANSFER                             5000.00     5000.00
        02/02/2025  ATM WITHDRAWAL                  200.00                  4800.00
        03/02/2025  GROCERY STORE PAYMENT           150.50                  4649.50
        04/02/2025  INTEREST EARNED                             25.00       4674.50
        05/02/2025  UTILITY BILL PAYMENT            89.75                   4584.75
        """

        try:
            result = await ai_processor_manager.extract_transactions_with_fallback(transaction_text)
            print(f"Extracted {len(result.transactions)} transactions:")
            for i, txn in enumerate(result.transactions, 1):
                print(f"  {i}. {txn.date} | {txn.description} | Debit: {txn.debit} | Credit: {txn.credit} | Balance: {txn.balance}")
        except Exception as e:
            print(f"Error in transaction extraction: {e}")
            import traceback
            traceback.print_exc()

    asyncio.run(test_bank_identification())
    asyncio.run(test_transaction_extraction())

if __name__ == "__main__":
    main()
