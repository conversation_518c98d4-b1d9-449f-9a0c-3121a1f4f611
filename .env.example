# AI Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Custom LLM Configuration
CUSTOM_LLM_ENDPOINT=https://your-custom-llm-endpoint.com/v1
CUSTOM_LLM_API_KEY=your_custom_llm_api_key_here

# Default AI Provider (openai, anthropic, google, mistral, deepseek, openrouter, custom)
DEFAULT_AI_PROVIDER=openai

# AI Model Configuration
OPENAI_MODEL=gpt-4
ANTHROPIC_MODEL=claude-3-sonnet-********
GOOGLE_MODEL=gemini-pro
MISTRAL_MODEL=mistral-large-latest
DEEPSEEK_MODEL=deepseek-chat
OPENROUTER_MODEL=anthropic/claude-3-sonnet

# AI Parameters
AI_TEMPERATURE=0.1
AI_MAX_TOKENS=4000
AI_TIMEOUT=30

# File Processing Configuration
MAX_FILE_SIZE_MB=50
SUPPORTED_FORMATS=pdf,csv,xlsx,xls,txt,jpg,jpeg,png
OCR_LANGUAGE=eng
ENABLE_OCR=true

# Output Configuration
OUTPUT_FORMAT=xlsx
SEPARATE_BANKS=true
MONTHLY_SHEETS=true
INCLUDE_SUMMARY=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=5

# Processing Configuration
BATCH_SIZE=10
MAX_CONCURRENT_FILES=3
RETRY_ATTEMPTS=3
RETRY_DELAY=1

# Paths Configuration
STATEMENTS_PATH=statements
PROCESSED_PATH=processed
OUTPUT_PATH=output
LOGS_PATH=logs
